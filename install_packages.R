# Install required R packages for sustainable meat analysis
cat("Installing required R packages...\n")

# Set CRAN mirror
options(repos = c(CRAN = "https://cran.rstudio.com/"))

# Install packages one by one to avoid dependency conflicts
packages_to_install <- c("readxl", "mlogit")

for(pkg in packages_to_install) {
  cat("Installing package:", pkg, "\n")
  tryCatch({
    install.packages(pkg, dependencies = TRUE, type = "binary")
    cat("Successfully installed:", pkg, "\n")
  }, error = function(e) {
    cat("Failed to install", pkg, ":", e$message, "\n")
  })
}

cat("\nBasic package installation completed!\n")
cat("You can now run a simplified analysis.\n")
